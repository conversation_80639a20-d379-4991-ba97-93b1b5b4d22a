<template>
  <div 
  class="details"
  v-loading="organizationDetailLoading"
  element-loading-text="正在加载组织详情,请稍后..."
  element-loading-spinner="el-icon-loading"
  element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <!-- 左侧 -->
    <el-card class="box-card">
      <div class="left">
        <!-- 组织信息 -->
        <div class="person-card">
          <div class="avatar">
            <div class="avatar-img">
              <el-avatar shape="square" :src="'/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' + personInfo.avatar" class="custom-avatar"></el-avatar>
            </div>
            <div class="avatar-info">
              <div class="info-row"><span class="info-label">组织名称：</span><span class="info-value">{{ personInfo.name }}</span></div>
              <div class="info-row"><span class="info-label">备注：</span><span class="info-value">{{ personInfo.remark }}</span></div>
              <div class="info-row"><span class="info-label">所属：</span><span class="info-value">{{ personInfo.belong }}</span></div>
              <div class="avatar-actions">
                <el-tooltip class="item" effect="dark" content="编辑" placement="top">
                  <el-button type="primary" icon="el-icon-edit" circle size="mini" @click="editPerson"></el-button>
                </el-tooltip>
                <!-- <el-tooltip class="item" effect="dark" content="拓扑关系" placement="top">
                  <el-button type="success" icon="el-icon-share" circle size="mini" @click="showRelation"></el-button>
                </el-tooltip> -->
                <el-tooltip class="item" effect="dark" content="删除" placement="top">
                  <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="deletePerson(personId)"></el-button>
                </el-tooltip>
              </div>
            </div>
          </div>
          <div class="other relation-collapse-content">
            <div class="relation-row">
              <span class="relation-label">创建时间：</span>
              <span class="relation-value">{{ personInfo.createTime }}</span>
            </div>
            <div class="relation-row">
              <span class="relation-label">描述：</span>
              <span class="relation-value">{{ personInfo.desi }}</span>
            </div>
            <div v-for="([key, value], index) in Object.entries(otherInfo)" :key="'other' + index" class="relation-row">
              <span class="relation-label">{{ key }}：</span>
              <span class="relation-value">{{ value }}</span>
            </div>
          </div>
        </div>
        <!-- 社交关系 -->
        <!-- <div class="relation-section">
            <el-collapse v-if="socializeMsg && socializeMsg.length">
              <el-collapse-item
                v-for="(item, idx) in socializeMsg"
                :key="'relation' + idx"
                :title="item.relationName + '：' + item.relationInfo.name"
              >
                <div class="relation-collapse-content">
                  <div class="relation-row">
                    <span class="relation-label">性别：</span
                    ><span class="relation-value">{{
                      item.relationInfo.sex
                    }}</span>
                  </div>
                  <div class="relation-row">
                    <span class="relation-label">出生日期：</span
                    ><span class="relation-value">{{
                      item.relationInfo.dateBirth
                    }}</span>
                  </div>
                  <div class="relation-row">
                    <span class="relation-label">年龄：</span
                    ><span class="relation-value">{{
                      item.relationInfo.age
                    }}</span>
                  </div>
                  <div class="relation-row">
                    <span class="relation-label">备注：</span
                    ><span class="relation-value">{{
                      item.relationInfo.remark
                    }}</span>
                  </div>
                  <div class="relation-row">
                    <span class="relation-label">身份证号：</span
                    ><span class="relation-value">{{
                      item.relationInfo.identity
                    }}</span>
                  </div>
                  <div class="relation-row">
                    <span class="relation-label">手机号：</span
                    ><span class="relation-value">{{
                      item.relationInfo.phone
                    }}</span>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
            <div
              v-else
              style="color: #bbb; font-size: 14px; padding: 8px 0 0 8px"
            >
              暂无社会关系
            </div>
        </div> -->
        <!-- 社交媒体分组 -->
        <div class="media-section">
          <el-collapse>
            <el-collapse-item
              v-for="(item, idx) in telegramSearchList"
              :key="'media-tg' + idx"
              :title="item.username && item.username.username ? 'Telegram ID：' + item.username.username : 'Telegram ID: 无'"
              :name="'telegram-' + idx"
            >
              <div class="media-collapse-content">
                <div class="media-row">
                  <span class="media-label">用户名：</span>
                  <span class="media-value">{{ item.username && item.username.username ? item.username.username : '无' }}</span>
                </div>
                <div class="media-row">
                  <span class="media-label">ID：</span>
                  <span class="media-value">{{ item.user_id && item.user_id.user_id ? item.user_id.user_id : '无' }}</span>
                </div>
                <div class="media-row" v-if="item.nickname && item.nickname.nickname">
                  <span class="media-label">昵称：</span>
                  <span class="media-value">{{ item.nickname.nickname }}</span>
                </div>
                <div class="media-row">
                  <span class="media-label">手机号：</span>
                  <span class="media-value">{{ item.telephone && item.telephone.telephone ? item.telephone.telephone : '无' }}</span>
                </div>
              </div>
            </el-collapse-item>
            <el-collapse-item
              v-for="(item, idx) in twitterSearchList"
              :key="'media-tw' + idx"
              :title="item.nickname && item.nickname.nickname ? 'Twitter ID：' + item.nickname.nickname : 'Twitter ID: 无'"
              :name="'twitter-' + idx"
            >
              <div class="twitter-card-box">
                <div class="twitter-card-main">
                  <el-avatar size="large" class="twitter-avatar">
                    <i class="el-icon-user-solid" style="font-size:32px;color:#ccc"></i>
                  </el-avatar>
                  <div class="twitter-info-right">
                    <div class="twitter-username-row">
                      <span class="twitter-label">昵称：</span>
                      <span class="twitter-username-value ellipsis" :title="item.nickname.nickname">
                        {{ item.nickname.nickname }}
                      </span>
                    </div>
                    <div class="twitter-summary-row">
                      <span class="twitter-label">简介：</span>
                      <span class="twitter-summary-value ellipsis" :title="item.summary || '暂无简介'">
                        {{ item.summary || '暂无简介' }}
                      </span>
                    </div>
                    <div class="twitter-stats-row">
                      <div class="twitter-stat-item">
                        <i class="el-icon-star-on twitter-stat-icon like"></i>
                        <span class="twitter-stat-num">{{ item.likes_count || 0 }}</span>
                      </div>
                      <div class="twitter-stat-item">
                        <i class="el-icon-user twitter-stat-icon follower"></i>
                        <span class="twitter-stat-num">{{ item.followers_count || 0 }}</span>
                      </div>
                      <div class="twitter-stat-item">
                        <i class="el-icon-user-solid twitter-stat-icon following"></i>
                        <span class="twitter-stat-num">{{ item.following_count || 0 }}</span>
                      </div>
                      <div class="twitter-stat-item">
                        <i class="el-icon-document twitter-stat-icon article"></i>
                        <span class="twitter-stat-num">{{ item.article_count || 0 }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-collapse-item>
            <el-collapse-item
              v-for="(item, idx) in linkedinSearchList"
              :key="'media-li' + idx"
              :title="item._source && item._source.nickname ? 'Linkedin ID：' + item._source.nickname : 'Linkedin ID: 无'"
              :name="'linkedin-' + idx"
            >
              <div class="media-collapse-content">
                <div class="media-row">
                  <span class="media-label">昵称：</span>
                  <span class="media-value">{{ item._source.nickname }}</span>
                </div>
                <div class="media-row">
                  <span class="media-label">ID：</span>
                  <span class="media-value">{{ item._source.user_id }}</span>
                </div>
                <div class="media-row">
                  <span class="media-label">内容：</span>
                  <span class="media-value">{{ item._source.content }}</span>
                </div>
              </div>
            </el-collapse-item>
            <el-collapse-item
              v-for="(item, idx) in facebookSearchList"
              :key="'media-fb' + idx"
              :title="item.name ? 'Facebook ID：' + item.name : 'Facebook ID: 无'"
              :name="'facebook-' + idx"
            >
              <div class="media-collapse-content">
                <div class="media-row">
                  <span class="media-label">用户名：</span>
                  <span class="media-value">{{ item.name }}</span>
                </div>
                <div class="media-row">
                  <span class="media-label">ID：</span>
                  <span class="media-value">{{ item.idNum }}</span>
                </div>
                <div class="media-row">
                  <span class="media-label">手机号：</span>
                  <span class="media-value">{{ item.phone }}</span>
                </div>
                <div class="media-row">
                  <span class="media-label">邮箱：</span>
                  <span class="media-value">{{ item.email }}</span>
                </div>
                <div class="media-row">
                  <span class="media-label">备注：</span>
                  <span class="media-value">{{ item.remark }}</span>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
          <div
            v-if="!(
              (telegramSearchList && telegramSearchList.length) ||
              (twitterSearchList && twitterSearchList.length) ||
              (linkedinSearchList && linkedinSearchList.length) ||
              (facebookSearchList && facebookSearchList.length)
            )"
            style="color: #bbb; font-size: 14px; padding: 8px 0 0 8px"
          >
            暂无社交媒体信息
          </div>
        </div>
      </div>
    </el-card>
    <!-- 右侧 -->
    <div class="right">
      <el-tabs v-model="activeName" @tab-click="handleClickTab">
        <el-tab-pane v-for="tab in organiBar" :key="tab.value" :label="tab.key" :name="tab.value">
          <component
            :is="componentMap[tab.value]"
            v-if="componentMap[tab.value] && activeName === tab.value"
            :key="`${tab.value}-${componentKey}`"
            :info="info"
          />
        </el-tab-pane>
      </el-tabs>
      <!-- 编辑 -->
      <organization-personnel v-if="organizationData.flag" :data="organizationData" :organiList="this.organiList" :info="info" />
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
export default {
  name: "Details",
  data() {
    return {
      personid: "",
      // 组织基本信息
      personInfo: {},
      // 其他信息
      otherInfo: [],
      // 社会关系
      socializeMsg: [],
      // 社交媒体信息
      socialMedia: [],
      activeName: "关系拓扑",
      componentKey: 0,
      // 编辑
      organizationData: {
        flag: false,
        title: "",
      },
      info: {},
      // 组件映射表
      componentMap: {
        关系拓扑: "organization-topology-map",
        facebook: "facebook",
        linkedin: "linkedin",
        telegram: "telegram",
        twitter: "twitter",
        public_opinion: "opinion",
      },
    };
  },
  computed: {
    ...mapState({
      organiList: (state) => state.organization.organiList,
      organiBar: (state) => state.organization.organiBar,
      organizationDetailLoading: (state) => state.organization.organizationDetailLoading,
      selectOrganiValue: (state) => state.organization.selectOrgani,
      twitterSearchList: (state) => state.organizationTopologyMap.twitterSearchList,
      telegramSearchList: (state) => state.organizationTopologyMap.telegramSearchList,
      linkedinSearchList: (state) => state.organizationTopologyMap.linkedinSearchList,
      facebookSearchList: (state) => state.organizationTopologyMap.facebookSearchList,
      personalRelations: (state) => state.organizationTopologyMap.personalRelations,
      organizationalRelations: (state) => state.organizationTopologyMap.organizationalRelations,
    }),
  },
  components: {
    "organization-personnel": () => import("@/layout/components/archives/OrganizationPersonnel.vue"), // 组织人员关系
    "organization-topology-map": () => import("@/layout/components/archives/organization-detail-components/organization-topology-map.vue"), // 组织关系拓扑图
    facebook: () => import("@/layout/components/searchListComponent/facebook-search-index.vue"),
    linkedin: () => import("@/layout/components/searchListComponent/linkedin.vue"),
    telegram: () => import("@/layout/components/telegram-search-components/telegram-search-index.vue"),
    twitter: () => import("@/layout/components/searchListComponent/twitter.vue"),
    opinion: () => import("@/layout/components/searchListComponent/publicOpinion.vue"),
  },
  created() {
    this.$store.commit("organization/sendOrganizationBar");
    this.$store.commit("organization/resetOrganizationDetailData");
    const queryData = localStorage.getItem('intelligence_organi');
    if (queryData) {
      const data = JSON.parse(queryData);
      this.selectOrgani(data);
    }
  },
  watch: {
    organizationDetailLoading: {
      handler(newVal) {
        this.$nextTick(() => {
          this.initData();
        });
      },
    },
  },
  methods: {
    ...mapMutations({
      selectOrgani: "organization/selectOrgani",
      setConditionsData: "search/conditions/setConditionsData",
    }),
    initData(d) {
      console.log("ddddddddddd", this.selectOrganiValue);     
      try {
        let data = null;
        if (d) {
          data = d;
        }else {
          data = this.selectOrganiValue;
        }
        this.info = data;
        this.personId = data._id;
        this.personInfo = { ...data._source.params.basic };
        this.otherInfo = data._source.params.customFields;
        this.socialMedia = { ...data._source.params.media };
        if (data._source.params.socialize && data._source.params.socialize.relation && data._source.params.socialize.relation[0].relationName != "") {
          this.socializeMsg = data._source.params.socialize.relation[0].relationName;
        } else {
          this.socializeMsg = [];
        }
      } catch (error) {
        console.error("解析数据时出错:", error);
      }
    },
    handleClickTab(tab, event) {
      this.activeName = tab.name;
      if (tab.name == "public_opinion") {
        this.setConditionsData(this.personName);
      }
      this.$nextTick(() => {
        this.componentKey++;
      });
    },
    editPerson() {
      this.organizationData = {
        flag: true,
        title: "编辑组织",
      };
    },
    showRelation() {},
    deletePerson(data) {
      this.$confirm("确定删除该目标组织吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          window.main.$store.commit("organization/deleteOri", data);
          this.$router.back();
        }).catch(() => {
          this.$message.info("已取消删除");
        });
    },
  },
};
</script>

<style src="../../../assets/scss/targetDetail.scss" lang="scss"></style>
<style scoped>
::v-deep .el-tabs__header {
  margin: 0;
}

::v-deep .el-tabs__content {
  padding: 0;
  height: calc(100vh - 100px);
}

::v-deep .el-tab-pane {
  height: 100%;
}

::v-deep .el-collapse-item__header {
  font-size: 12px;
  font-weight: 600;
  padding: 5px;
}

::v-deep .custom-avatar {
  width: 150px;
  height: 210px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

::v-deep .custom-avatar .el-avatar__image {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.section-title {
  font-size: 17px;
  font-weight: 700;
  color: #333;
  margin: 18px 0 8px 8px;
  border-left: 3px solid #409EFF;
  padding-left: 8px;
}
.media-card {
  border-radius: 8px;
  margin-bottom: 10px;
  background: #f8fafd;
  border: 1px solid #e6e8eb;
  box-shadow: none;
  padding: 10px 14px;
}
.media-title {
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 4px;
}
.media-info {
  font-size: 14px;
  color: #444;
  margin-bottom: 2px;
}
</style>