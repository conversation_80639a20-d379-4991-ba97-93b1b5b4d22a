<template>
  <div class="intellManage">
    <!-- 头部信息 -->
    <div class="header">
      <div class="left">
        <div class="search-container">
          <el-button 
            style="margin-right: 10px;"
            type="primary"  
            @click="handleRefresh"
          >刷新</el-button>
          <el-input
            placeholder="输入关键字匹配目标人"
            v-model="searchKeyword"
            clearable
            @keyup.enter.native="handleEnter"
            @clear="handleClear"
            class="search-input"
          >
          </el-input>
          <el-button 
            type="primary"  
            @click="handleEnter"
            class="search-btn"
          >搜索</el-button>
        </div>
        <div class="switch-person">
          <span>展示次要目标人</span>
          <el-switch
            v-model="showSecondaryPerson"
          />
        </div>
      </div>
      <el-button
        type="primary"
        icon="el-icon-plus"
        class="add-btn"
        @click="openOrganPerson"
      >添加目标人</el-button>
    </div>
    <!-- 搜索结果头部 -->
    <div v-if="showSearchHeader" class="search-header-bar">
      <el-button type="text" icon="el-icon-arrow-left" @click="handleBack">返回</el-button>
      <span style="margin-left: 10px;">搜索关键词：{{ lastSearchKeyword }}</span>
    </div>
    <!-- 主体部分，身份证样式卡片 -->
    <div class="container">
      <div
        class="id-card"
        v-for="(item, index) in personList"
        :key="index"
        @click="clickCard(item)"
      >
        <!-- 身份证正面 -->
        <div class="id-card-front">
          <div v-if="item._source.type === 'secondary_key_person'" class="secondary-badge">次要</div>
          <div class="id-card-content">
            <div class="id-card-photo">
              <el-avatar 
                :size="120" 
                :src="'/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' + item._source.params.basic.avatar"
                @error="handleAvatarError"
              >
                <img src="@/assets/images/user.png" style="width: 100%;" />
              </el-avatar>
            </div>
            
            <div class="id-card-info">
              <div class="info-row">
                <span class="label">姓名:</span>
                <span class="value" :title="item._source.params.basic.name" @click.stop>{{ item._source.params.basic.name }}</span>
              </div>
              <div class="info-row remark">
                <span class="label">备注:</span>
                <span class="value" :title="item._source.params.basic.remark || '暂无备注'" @click.stop>{{ item._source.params.basic.remark || '暂无备注' }}</span>
              </div>
              <div class="info-row">
                <span class="label">性别:</span>
                <span class="value" :title="item._source.params.basic.sex" @click.stop>{{ item._source.params.basic.sex }}</span>
                <span class="label" style="margin-left: 10px;">年龄:</span>
                <span class="value" :title="item._source.params.basic.age" @click.stop>{{ item._source.params.basic.age }}</span>
              </div>
              <div class="info-row">
                <span class="label">手机号:</span>
                <span class="value" :title="item._source.params.basic.phone" @click.stop>{{ item._source.params.basic.phone }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 卡片悬停效果 -->
        <div class="id-card-hover">
        </div>
      </div>

      <!-- 空状态展示 -->
      <el-empty 
        v-if="personList.length === 0" 
        description="暂无人员数据"
        class="empty-state"
      >
        <el-button type="primary" @click="openOrganPerson">添加目标人</el-button>
      </el-empty>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-if="total > 0"
        :total="total"
        :page-size="size"
        :current-page="currentPage"
        @current-change="handleCurrentChange"
        layout="total, prev, pager, next, jumper"
        background
      />
    </div>

    <!-- 引入组织人员关系组件 -->
    <personnel-organization 
      v-if="organizationData.flag"
      :data="organizationData" 
      @close="handleOrganizationClose" 
    />
  </div>
</template>

<script>
import { mapMutations, mapState } from "vuex";
export default {
  data() {
    return {
      personData: [],
      organizationData: {
        flag: false,
        title: "",
      },
      showSearchHeader: false, // 控制返回按钮和关键词显示
      lastSearchKeyword: "",  // 记录上次搜索关键词
    };
  },
  components: {
    "personnel-organization": () =>
      import("@/layout/components/archives/PersonnelOrganization.vue"), // 组织人员关系
  },
  mounted() {},
  created() {
    this.resetAllData();
    this.getPerson();
  },
  computed: {
    ...mapState({
      personList: (state) => state.person.personList,
      total: (state) => state.person.total,
      size: (state) => state.person.size,
      currentPage: (state) => state.person.from / state.person.size + 1,
    }),
    showSecondaryPerson: {
      get() {
        return this.$store.state.person.showSecondaryPerson;
      },
      set(value) {
        this.setShowSecondary(value);
        this.getPerson();
      }
    },
    searchKeyword: {
      get() {
        return this.$store.state.person.searchKeyword;
      },
      set(value) {
        this.setSearchKeyword(value);
      }
    },
  },
  methods: {
    ...mapMutations({
      resetPersonData: "person/resetPersonData",
      resetAllData: "person/resetAllData",
      getPerson: "person/getPerson",
      setSearchKeyword: "person/setSearchKeyword",
      setPage: "person/setPage",
      setShowSecondary: "person/setShowSecondary",
    }),

    // 刷新
    handleRefresh() {
      this.resetPersonData();
      this.getPerson()
    },
    
    // 分页
    handleCurrentChange(page) {
      this.setPage(page)
      this.getPerson()
    },
    
    // 搜索框回车事件
    handleEnter() {
      this.resetPersonData();
      if (!this.searchKeyword || !this.searchKeyword.trim()) {
        this.showSearchHeader = false;
        this.lastSearchKeyword = "";
      } else {
        this.showSearchHeader = true;
        this.lastSearchKeyword = this.searchKeyword;
      }
      this.getPerson();
    },
    
    // 搜索框清除事件
    handleClear() {
      this.setSearchKeyword("");
    },

    // 返回按钮点击事件
    handleBack() {
      this.setSearchKeyword("");
      this.showSearchHeader = false;
      this.lastSearchKeyword = "";
      this.resetPersonData();
      this.getPerson();
    },

    // 打开组织人员关系组件
    openOrganPerson() {
      this.organizationData = {
        flag: true,
        title: "添加目标人",
      };      
    },

    // 点击卡片
    clickCard(data) {
      let person = JSON.stringify(data);
      localStorage.setItem('intelligence_person', person);
      const routeData = this.$router.resolve({
        path: "/personDetails",
      });
      window.open(routeData.href, "_blank");
    },

    // 头像错误
    handleAvatarError() {
      return true // 使用默认头像
    },

    // 组织人员关系组件关闭
    handleOrganizationClose() {
      this.organizationData.flag = false
    },


  },
};
</script>

<style lang="scss" scoped>
.intellManage {
  padding: 24px;
  min-height: 100%;
  background: #f0f2f5;
  height: 100%;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    height: 5%;
    
    .left {
      flex: 1;
      max-width: 450px;
      display: flex;
      align-items: center;
      
      .search-container {
        display: flex;
        align-items: center;
        
        .search-input {
          flex: 1;
          :deep(.el-input__inner) {
            height: 40px;
            border-radius: 4px 0 0 4px;
            border-right: none;
            
            &:focus {
              border-color: #409eff;
            }
          }
        }
        
        .search-btn {
          height: 40px;
          border-radius: 0 4px 4px 0;
          padding: 0 15px;
          margin-left: -1px;
        }
      }
      
      .switch-person {
        margin-left: 20px;
        display: flex;
        align-items: center;
        padding: 6px 12px;
        
        span {
          margin-right: 8px;
          font-size: 14px;
          color: #606266;
          white-space: nowrap;
        }
        
        :deep(.el-switch) {
          .el-switch__core {
            height: 20px;
            border-radius: 10px;
          }
          
          .el-switch__label {
            font-size: 12px;
          }
        }
      }
    }
    
    .add-btn {
      margin-left: 16px;
    }
  }

  .container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
    gap: 20px;
    padding: 8px;
    overflow-y: auto;
    max-height: calc(100vh - 180px);
    height: 90%;
    
    .id-card {
      position: relative;
      width: 80%;
      height: 170px;
      perspective: 1000px;
      cursor: pointer;
      
      .id-card-front {
        position: relative;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%);
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        padding: 20px;
        transition: all 0.3s ease;
        border: 1px solid #e8e8e8;
        overflow: hidden;
        
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 6px;
          background: linear-gradient(90deg, #1890ff, #36cfc9);
        }
        
        .id-card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          
          .id-card-title {
            font-size: 18px;
            font-weight: bold;
            color: #1890ff;
            letter-spacing: 1px;
          }
          
          .id-card-type {
            font-size: 14px;
            color: #666;
            background: #e6f7ff;
            padding: 2px 8px;
            border-radius: 4px;
          }
        }
        
        .id-card-content {
          display: flex;
          gap: 20px;
          
          .id-card-photo {
            width: 100px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            :deep(.el-avatar) {
              width: 100% !important;
              height: 100% !important;
              border-radius: 12px !important;
              object-fit: cover;
              box-shadow: 0 2px 8px rgba(0,0,0,0.12);
            }
          }
          
          .id-card-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            
            .info-row {
              display: flex;
              margin-bottom: 8px;
              height: 25px;
              line-height: 25px;
              .label {
                width: auto;
                color: #666;
                font-size: 14px;
              }
              
              .value {
                margin-left: 5px;
                flex: 1;
                color: #333;
                font-size: 14px;
                font-weight: 500;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                cursor: default;
              }
              
              &.remark {
                .value {
                  color: #666;
                  font-size: 13px;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                }
              }
            }
          }
        }
        
        .id-card-footer {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          padding: 12px 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: rgba(0, 0, 0, 0.02);
          border-top: 1px dashed #e8e8e8;
          
          .id-card-qrcode {
            color: #1890ff;
            font-size: 20px;
          }
          
          .id-card-number {
            font-size: 12px;
            color: #999;
            font-family: monospace;
          }
        }
      }
      
      .id-card-hover {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        // background: rgba(24, 143, 255, 0.24);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
        opacity: 0;
        transition: opacity 0.3s;
        border-radius: 12px;
        
        i {
          font-size: 32px;
          margin-bottom: 8px;
        }
        
        span {
          font-size: 16px;
          font-weight: 500;
        }
      }
      
      &:hover {
        .id-card-front {
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }
        
        .id-card-hover {
          opacity: 1;
        }
      }
    }
    
    .empty-state {
      grid-column: 1 / -1;
      margin-top: 120px;
    }
  }

  .pagination {
    margin-top: .625rem;
    display: flex;
    justify-content: center;
    position: relative;
    bottom: 0;
    width: 100%;
    
    :deep(.el-pagination) {
      padding: 0;
      font-weight: normal;
      
      .el-pagination__total {
        margin-right: 16px;
      }
      
      .btn-prev, .btn-next {
        background-color: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        
        &:hover {
          color: #1890ff;
          border-color: #1890ff;
        }
      }
      
      .el-pager li {
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        margin: 0 4px;
        
        &:hover {
          color: #1890ff;
          border-color: #1890ff;
        }
        
        &.active {
          background-color: #1890ff;
          color: #fff;
          border-color: #1890ff;
        }
      }
      
      .el-pagination__jump {
        margin-left: 16px;
        
        .el-input__inner {
          height: 28px;
          line-height: 28px;
        }
      }
    }
  }
}

.search-header-bar {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 15px;
}

.secondary-badge {
  position: absolute;
  top: 5px;
  right: 10px;
  color: #E6A23C;
  font-size: 13px;
  z-index: 2;
}

// 响应式布局
@media screen and (max-width: 768px) {
  .intellManage {
    padding: 16px;
    
    .header {
      flex-direction: column;
      align-items: stretch;
      
      .left {
        width: 100%;
        max-width: 100%;
        margin-bottom: 16px;
        
        .search-container {
          width: 100%;
        }
      }
      
      .add-btn {
        margin-left: 0;
      }
    }
    
    .container {
      grid-template-columns: 1fr;
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .intellManage {
    background: #141414;
    
    .id-card-front {
      background: linear-gradient(135deg, #1f1f1f 0%, #141414 100%);
      border-color: #303030;
      
      .id-card-header {
        .id-card-title {
          color: #1890ff;
        }
        
        .id-card-type {
          background: #111b26;
          color: #a3a6ad;
        }
      }
      
      .id-card-info {
        .info-row {
          .label {
            color: #a3a6ad;
          }
          
          .value {
            color: #e5eaf3;
          }
          
          &.remark .value {
            color: #a3a6ad;
          }
        }
      }
      
      .id-card-footer {
        background: rgba(255, 255, 255, 0.02);
        border-color: #303030;
      }
    }

    .pagination {
      :deep(.el-pagination) {
        color: #e5eaf3;
        
        .btn-prev, .btn-next {
          background-color: #1f1f1f;
          border-color: #303030;
          color: #e5eaf3;
          
          &:hover {
            color: #1890ff;
            border-color: #1890ff;
          }
        }
        
        .el-pager li {
          background-color: #1f1f1f;
          border-color: #303030;
          color: #e5eaf3;
          
          &:hover {
            color: #1890ff;
            border-color: #1890ff;
          }
          
          &.active {
            background-color: #1890ff;
            color: #fff;
            border-color: #1890ff;
          }
        }
        
        .el-pagination__jump {
          color: #a3a6ad;
          
          .el-input__inner {
            background-color: #1f1f1f;
            border-color: #303030;
            color: #e5eaf3;
          }
        }
      }
    }
  }
}
</style>