<template>
  <div class="OrigDialog">
    <el-dialog 
      :title="data.title" 
      :visible.sync="data.flag" 
      width="80%" 
      append-to-body 
      top="10px" 
      :close-on-click-modal="false" 
      :close-on-press-escape="false" 
      :before-close="handleClose"
    >
      <template #title>
        <span>{{ data.title }}</span>
        <el-button 
          type="primary" 
          size="small" 
          style="margin-left: 16px;"
          @click="showSecondaryPersonDialog">
          添加目标人</el-button>
      </template> 
      <div class="container-dialog">
        <div :class="{'left': true, 'left-with-panel': personFlag}">
          <el-tabs v-model="activeName" tab-position="top" @tab-click="handleClickTabs" @tab-remove="handleTabRemove">
            <!-- 主组织信息 -->
            <el-tab-pane label="组织信息" name="info">
              <el-form class="personForm" ref="basicForm" :model="basicForm" :rules="basicRules" label-width="100px" size="small">
                <el-form-item label="LOGO" title="LOGO">
                  <div class="avatar-uploader">
                    <el-upload class="avatar-uploader" action="https://jsonplaceholder.typicode.com/posts/"
                      :show-file-list="false" :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
                      <el-avatar v-if="basicForm.avatar" :size="80" :src="basicForm.avatar" class="avatar" />
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                  </div>
                </el-form-item>
                <el-form-item label="组织名称" prop="name" title="组织名称">
                  <el-input v-model="basicForm.name"></el-input>
                </el-form-item>
                <el-form-item label="备注" title="备注">
                  <el-input v-model="basicForm.remark"></el-input>
                </el-form-item>
                <el-form-item label="创建时间" title="创建时间">
                  <el-date-picker v-model="basicForm.createTime" type="date" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" placeholder="选择日期">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="归属" title="归属">
                  <el-input v-model="basicForm.belong"></el-input>
                </el-form-item>
                <el-form-item label="描述" title="描述">
                  <el-input v-model="basicForm.desi"></el-input>
                </el-form-item>

                <!-- 社交媒体账号 -->
                <el-form-item label="邮箱:" title="邮箱:">
                  <div class="social-input-container">
                    <el-input v-model="newEmail" placeholder="请输入邮箱" class="social-input" @keydown.enter.native="addSocialAccount('emails')" @blur="addSocialAccount('emails')"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSocialAccount('emails')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag v-for="(email, index) in basicForm.emails" :key="'email-' + index" closable @close="removeSocialAccount('emails', index)">
                      {{ email }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="手机号:" title="手机号:">
                  <div class="social-input-container">
                    <el-select v-model="newPhoneAreaCode" placeholder="区号" class="social-input-area-code" filterable>
                      <el-option v-for="item in areaCodes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                    <el-input v-model="newPhoneNumber" placeholder="请输入电话号" class="social-input-phone-number" maxlength="30" @input="newPhoneNumber = newPhoneNumber.replace(/[^\d]/g, '')" @keydown.enter.native="addSocialAccount('phoneNumbers')"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSocialAccount('phoneNumbers')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag v-for="(phone, index) in basicForm.phoneNumbers" :key="'phone-' + index" closable @close="removeSocialAccount('phoneNumbers', index)">
                      {{ phone }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="Telegram ID:" title="Telegram ID:">
                  <div class="social-input-container">
                    <el-input v-model="newTelegramId" placeholder="请输入Telegram ID" class="social-input" @keydown.enter.native="addSocialAccount('telegramIds')"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSocialAccount('telegramIds')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag v-for="(id, index) in basicForm.telegramIds" :key="'telegram-' + index" closable @close="removeSocialAccount('telegramIds', index)">
                      {{ id }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="Twitter ID:" title="Twitter ID:">
                  <div class="social-input-container">
                    <el-input v-model="newTwitterId" placeholder="请输入Twitter ID" class="social-input"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSocialAccount('twitterIds')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag v-for="(id, index) in basicForm.twitterIds" :key="'twitter-' + index" closable @close="removeSocialAccount('twitterIds', index)">
                      {{ id }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="Facebook ID:" title="Facebook ID:">
                  <div class="social-input-container">
                    <el-input v-model="newFacebookId" placeholder="请输入Facebook ID" class="social-input"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSocialAccount('facebookIds')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag v-for="(id, index) in basicForm.facebookIds" :key="'facebook-' + index" closable @close="removeSocialAccount('facebookIds', index)">
                      {{ id }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="LinkedIn ID:" title="LinkedIn ID:">
                  <div class="social-input-container">
                    <el-input v-model="newLinkedInId" placeholder="请输入LinkedIn ID" class="social-input"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSocialAccount('linkedInIds')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag v-for="(id, index) in basicForm.linkedInIds" :key="'linkedin-' + index" closable @close="removeSocialAccount('linkedInIds', index)">
                      {{ id }}
                    </el-tag>
                  </div>
                </el-form-item>

                <!-- 其他信息 -->
                <el-form-item v-for="(value, fieldName) in basicForm.customFields" :key="fieldName" :label="fieldName + ':'" :title="fieldName + ':'" class="custom-field-item">
                  <div class="social-input-container">
                    <el-input v-model="basicForm.customFields[fieldName]" :placeholder="`请输入内容`" class="social-input"></el-input>
                    <el-button type="danger" icon="el-icon-delete" @click="removeCustomField(fieldName)"></el-button>
                  </div>
                </el-form-item>
                <el-form-item label="其他信息" title="其他信息">
                  <el-button type="primary" size="mini" icon="el-icon-plus" @click="showAddFieldDialog"></el-button>
                </el-form-item>

                <!-- 关系 -->
                <el-form-item label="关系" title="关系">
                  <div class="relation-box-container">
                    <el-button type="primary" size="mini" icon="el-icon-plus" @click.prevent="openRelationPanel()"></el-button>
                    <div v-for="(item, index) in basicForm.relation" :key="index" class="relation-item">
                      <div class="relation-content">
                        <div class="relation-avatar">
                          <el-tag type="primary" v-if="item.intellValue && item.intellValue[0] === origanList[1].label">个人关系</el-tag>
                          <el-tag type="success" v-else-if="item.intellValue && item.intellValue[0] === origanList[0].label">组织关系</el-tag>
                          <el-tag type="warning" v-else-if="item.intellValue && item.intellValue[0] === origanList[2].label">次要目标人</el-tag>
                          <img class="relation-avatar-img" src="@/assets/images/user.png" />
                          <span class="relation-name">{{ getRelationName(item) }}</span>
                          <div class="relation-input">
                            <span>关系：</span>
                            <el-input v-if="item.intellValue && item.intellValue[0] === origanList[0].label" v-model="item.organizationRelation" placeholder="请输入组织关系" maxlength="20" show-word-limit></el-input>
                            <el-input v-else-if="item.intellValue && item.intellValue[0] === origanList[1].label" v-model="item.personRelation" placeholder="请输入个人关系" maxlength="20" show-word-limit></el-input>
                            <el-input v-else-if="item.intellValue && item.intellValue[0] === origanList[2].label" v-model="item.personRelation" placeholder="请输入个人关系" maxlength="20" show-word-limit></el-input>
                          </div>
                          <el-button type="danger" icon="el-icon-delete" circle size="mini" @click.prevent="removeRelation(item)"></el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 次要目标人信息 -->
            <el-tab-pane v-for="(secondaryPerson, index) in secondaryPersons" :key="'secondary-' + index" :label="secondaryPerson.name" :name="'secondary-' + index" closable>
              <el-form class="personForm" :ref="'secondaryForm-' + index" :model="secondaryPerson" :rules="secondaryPersonRules" label-width="100px" size="small">
                <el-form-item label="目标人类型" title="目标人类型">
                  <el-radio-group v-model="secondaryPerson.type">
                    <el-radio value="key_person" label="key_person">目标人</el-radio>
                    <el-radio value="secondary_key_person" label="secondary_key_person">次要目标人</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="关系" title="关系">
                  <el-input v-model="secondaryPerson.relationDesc" placeholder="请输入与主组织的关系"></el-input>
                </el-form-item>
                <el-form-item label="头像" title="头像">
                  <div class="avatar-uploader">
                    <el-upload class="avatar-uploader" action="https://jsonplaceholder.typicode.com/posts/"
                      :show-file-list="false" :on-success="(res, file) => handleSecondaryAvatarSuccess(res, file, index)" :before-upload="beforeAvatarUpload">
                      <el-avatar v-if="secondaryPerson.avatar" :size="80" :src="secondaryPerson.avatar" class="avatar" />
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                  </div>
                </el-form-item>
                <el-form-item label="姓名" prop="name" title="姓名">
                  <el-input v-model="secondaryPerson.name"></el-input>
                </el-form-item>
                 <el-form-item label="备注" title="备注">
                  <el-input v-model="secondaryPerson.remark"></el-input>
                </el-form-item>
                <el-form-item label="出生日期" prop="dateBirth" title="出生日期">
                  <el-date-picker v-model="secondaryPerson.dateBirth" type="date" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" placeholder="选择日期" @change="(date) => calculateSecondaryAge(index, date)"
                    :picker-options="birthDatePickerOptions">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="年龄" prop="age" title="年龄">
                  <el-input v-model="secondaryPerson.age" readonly :placeholder="secondaryPerson.age === null ? '出生日期不能为未来时间' : ''"></el-input>
                </el-form-item>
                <el-form-item label="性别" title="性别">
                  <el-radio-group v-model="secondaryPerson.sex">
                    <el-radio value="男" label="男"></el-radio>
                    <el-radio value="女" label="女"></el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="身份证号" prop="identity" title="身份证号">
                  <el-input v-model="secondaryPerson.identity"></el-input>
                </el-form-item>
                <el-form-item label="邮箱:" title="邮箱:">
                  <div class="social-input-container">
                    <el-input v-model="secondaryNewInputs[index].email" placeholder="请输入邮箱" class="social-input" @keydown.enter.native="addSecondarySocialAccount(index, 'emails')" @blur="addSecondarySocialAccount(index, 'emails')"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSecondarySocialAccount(index, 'emails')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag v-for="(email, emailIndex) in secondaryPerson.emails" :key="'secondary-email-' + index + '-' + emailIndex" closable @close="removeSecondarySocialAccount(index, 'emails', emailIndex)">
                      {{ email }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="手机号:" title="手机号:">
                  <div class="social-input-container">
                    <el-select v-model="secondaryNewInputs[index].phoneAreaCode" placeholder="区号" class="social-input-area-code" filterable>
                      <el-option
                        v-for="item in areaCodes"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                    <el-input 
                      v-model="secondaryNewInputs[index].phoneNumber" 
                      placeholder="请输入电话号" 
                      class="social-input-phone-number"
                      maxlength="30"
                      @input="secondaryNewInputs[index].phoneNumber = secondaryNewInputs[index].phoneNumber.replace(/[^\d]/g, '')"
                      @keydown.enter.native="addSecondarySocialAccount(index, 'phoneNumbers')"
                    ></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSecondarySocialAccount(index, 'phoneNumbers')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag
                      v-for="(phone, phoneIndex) in secondaryPerson.phoneNumbers"
                      :key="'secondary-phone-' + index + '-' + phoneIndex"
                      closable
                      @close="removeSecondarySocialAccount(index, 'phoneNumbers', phoneIndex)"
                    >
                      {{ phone }}
                    </el-tag>
                  </div>
                </el-form-item>
                
                <!-- 次要目标人社交媒体 -->
                <el-form-item label="Telegram ID:" title="Telegram ID:">
                  <div class="social-input-container">
                    <el-input v-model="secondaryNewInputs[index].telegramId" placeholder="请输入Telegram ID" class="social-input" @keydown.enter.native="addSecondarySocialAccount(index, 'telegramIds')"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSecondarySocialAccount(index, 'telegramIds')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag v-for="(id, idIndex) in secondaryPerson.telegramIds" :key="'secondary-telegram-' + index + '-' + idIndex" closable @close="removeSecondarySocialAccount(index, 'telegramIds', idIndex)">
                      {{ id }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="Twitter ID:" title="Twitter ID:">
                  <div class="social-input-container">
                    <el-input v-model="secondaryNewInputs[index].twitterId" placeholder="请输入Twitter ID" class="social-input" @keydown.enter.native="addSecondarySocialAccount(index, 'twitterIds')"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSecondarySocialAccount(index, 'twitterIds')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag v-for="(id, idIndex) in secondaryPerson.twitterIds" :key="'secondary-twitter-' + index + '-' + idIndex" closable @close="removeSecondarySocialAccount(index, 'twitterIds', idIndex)">
                      {{ id }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="Facebook ID:" title="Facebook ID:">
                  <div class="social-input-container">
                    <el-input v-model="secondaryNewInputs[index].facebookId" placeholder="请输入Facebook ID" class="social-input" @keydown.enter.native="addSecondarySocialAccount(index, 'facebookIds')"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSecondarySocialAccount(index, 'facebookIds')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag v-for="(id, idIndex) in secondaryPerson.facebookIds" :key="'secondary-facebook-' + index + '-' + idIndex" closable @close="removeSecondarySocialAccount(index, 'facebookIds', idIndex)">
                      {{ id }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="LinkedIn ID:" title="LinkedIn ID:">
                  <div class="social-input-container">
                    <el-input v-model="secondaryNewInputs[index].linkedInId" placeholder="请输入LinkedIn ID" class="social-input" @keydown.enter.native="addSecondarySocialAccount(index, 'linkedInIds')"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSecondarySocialAccount(index, 'linkedInIds')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag v-for="(id, idIndex) in secondaryPerson.linkedInIds" :key="'secondary-linkedin-' + index + '-' + idIndex" closable @close="removeSecondarySocialAccount(index, 'linkedInIds', idIndex)">
                      {{ id }}
                    </el-tag>
                  </div>
                </el-form-item>

                <!-- 次要目标人其他信息 -->
                 <el-form-item v-for="(value, fieldName) in secondaryPerson.customFields" :key="'secondary-field-' + index + '-' + fieldName" :label="fieldName + ':'" :title="fieldName + ':'" class="custom-field-item">
                  <div class="social-input-container">
                    <el-input v-model="secondaryPerson.customFields[fieldName]" :placeholder="`请输入内容`" class="social-input"></el-input>
                    <el-button type="danger" icon="el-icon-delete" @click="removeSecondaryCustomField(index, fieldName)"></el-button>
                  </div>
                </el-form-item>
                <el-form-item label="其他信息" title="其他信息">
                  <el-button type="primary" size="mini" icon="el-icon-plus" @click="showSecondaryAddFieldDialog(index)"></el-button>
                </el-form-item>
                
                <!-- 次要目标人关系 -->
                <el-form-item label="关系" title="关系">
                  <div class="relation-box-container">
                    <el-button type="primary" size="mini" icon="el-icon-plus" @click.prevent="openRelationPanel(index)"></el-button>
                    <div v-for="(item, itemIndex) in secondaryPerson.relation" :key="'secondary-relation-' + index + '-' + itemIndex" class="relation-item">
                       <div class="relation-content">
                        <div class="relation-avatar">
                          <el-tag type="primary" v-if="item.intellValue && item.intellValue[0] === origanList[1].label">个人关系</el-tag>
                          <el-tag type="success" v-else-if="item.intellValue && item.intellValue[0] === origanList[0].label">组织关系</el-tag>
                          <el-tag type="warning" v-else-if="item.intellValue && item.intellValue[0] === origanList[2].label">次要目标人</el-tag>
                          <img class="relation-avatar-img" src="@/assets/images/user.png" />
                          <span class="relation-name">{{ getRelationName(item) }}</span>
                          <div class="relation-input">
                            <span>关系：</span>
                            <el-input v-if="item.intellValue && item.intellValue[0] === origanList[0].label" v-model="item.organizationRelation" placeholder="请输入组织关系" maxlength="20" show-word-limit></el-input>
                            <el-input v-else-if="item.intellValue && item.intellValue[0] === origanList[1].label" v-model="item.personRelation" placeholder="请输入个人关系" maxlength="20" show-word-limit></el-input>
                            <el-input v-else-if="item.intellValue && item.intellValue[0] === origanList[2].label" v-model="item.personRelation" placeholder="请输入个人关系" maxlength="20" show-word-limit></el-input>
                          </div>
                          <el-button type="danger" icon="el-icon-delete" circle size="mini" @click.prevent="removeSecondaryRelation(index, item)"></el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div v-if="personFlag" class="right">
          <organiRelationVue 
            ref="organiRelationRef"
            :showFlag="personFlag" 
            :initialSelectedOrganizations="selectedOrganizations"
            :initialSelectedPersons="selectedPersons"
            :initialSelectedSecondaryPersons="selectedSecondaryPersons"
            @selectOrganizations="handleSelectOrganizations"
            @selectPersons="handleSelectPersons"
            @selectSecondaryPersons="handleSelectSecondaryPersons"
          ></organiRelationVue>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" v-if="data.title == '添加组织'" @click="submitForm">提交</el-button>
        <el-button type="primary" v-else @click="editForm">保存</el-button>
      </div>
    </el-dialog>
    
    <!-- 添加次要目标人弹窗 -->
    <el-dialog title="添加次要目标人" :visible.sync="secondaryPersonDialogVisible" width="30%" append-to-body>
      <el-form :model="secondaryPersonForm" :rules="secondaryPersonRules" ref="secondaryPersonForm" label-width="100px">
        <el-form-item label="姓名" prop="name" title="姓名">
          <el-input v-model="secondaryPersonForm.name" placeholder="请输入次要目标人姓名"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="secondaryPersonDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addSecondaryPersonFn">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 添加字段弹窗（主组织） -->
    <el-dialog title="添加字段" :visible.sync="addFieldDialogVisible" width="30%" append-to-body>
      <el-form :model="newFieldForm" :rules="newFieldRules" ref="newFieldForm" label-width="100px">
        <el-form-item label="字段名" prop="name" title="字段名">
          <el-input v-model="newFieldForm.name" placeholder="请输入字段名"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addFieldDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addCustomField">确 定</el-button>
      </div>
    </el-dialog>
    
    <!-- 添加字段弹窗（次要目标人） -->
    <el-dialog title="添加字段" :visible.sync="secondaryAddFieldDialogVisible" width="30%" append-to-body>
      <el-form :model="secondaryNewFieldForm" :rules="newFieldRules" ref="secondaryNewFieldForm" label-width="100px">
        <el-form-item label="字段名" prop="name" title="字段名">
          <el-input v-model="secondaryNewFieldForm.name" placeholder="请输入字段名"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="secondaryAddFieldDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addSecondaryCustomField">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// <script>部分与上一回答相同，这里不再重复，请使用上一回答中完整的<script>部分
import { mapState } from "vuex";
import organiRelationVue from "./organiRelation.vue";

export default {
  name: "OrganizationDialog",
  components: {
    organiRelationVue,
  },
  props: {
    data: {
      type: Object,
      required: true,
    },
    info: {
      type: Object,
    },
  },
  data() {
    // 自定义验证器
    const validateIdentity = (rule, value, callback) => {
      if (value && value.trim()) {
        if (value.length > 18) {
          callback(new Error('身份证号长度不能超过18位'));
          return;
        } else {
          const idCardRegex = /^\d{1,17}(\d|X|x)?$/;
          if (!idCardRegex.test(value)) {
            callback(new Error('身份证号格式不正确，允许最后一位是字母'));
          } else {
            callback();
          }
        }
      } else {
        callback();
      }
    };

    const validateBirthDate = (rule, value, callback) => {
      if (value) {
        const birthDate = new Date(value);
        const currentDate = new Date();
        if (birthDate > currentDate) {
          callback(new Error('出生日期不能选择未来时间'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };

    const validateEmail = (rule, value, callback) => {
      if (value && value.trim()) {
        const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
        if (!emailRegex.test(value)) {
          callback(new Error('邮箱格式不正确'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };

    return {
      activeName: "info",
      personFlag: false,

      // 主组织基础信息
      basicForm: {
        avatar: "",
        name: "",
        remark: "",
        createTime: "",
        belong: "",
        desi: "",
        relation: [],
        emails: [], // 邮箱
        phoneNumbers: [], // 手机号
        telegramIds: [],
        twitterIds: [],
        facebookIds: [],
        linkedInIds: [],
        customFields: {},
      },
      basicRules: {
        name: [{ required: true, message: '请输入组织名称', trigger: 'blur' }],
      },

      // 次要目标人相关
      secondaryPersons: [],
      secondaryPersonDialogVisible: false,
      secondaryPersonForm: { name: '' },
      secondaryPersonRules: {
        name: [{ required: true, message: '请输入次要目标人姓名', trigger: 'blur' }],
        dateBirth: [
          { validator: validateBirthDate, trigger: 'change' }
        ],
        age: [
          { type: 'number', min: 0, message: '年龄不能为负数', trigger: 'blur', transform: value => Number(value) }
        ],
        identity: [
          { validator: validateIdentity, trigger: ['blur', 'change'] }
        ],
        email: [
          { validator: validateEmail, trigger: 'blur' }
        ]
      },
      secondaryNewInputs: [], // 用于存储每个次要目标人的临时输入值

      // 社交媒体临时输入
      newEmail: '',
      newPhoneAreaCode: '+86',
      newPhoneNumber: '',
      newTelegramId: '',
      newTwitterId: '',
      newFacebookId: '',
      newLinkedInId: '',

      // 其他信息（自定义字段）相关
      addFieldDialogVisible: false,
      newFieldForm: { name: '' },
      newFieldRules: {
        name: [{ required: true, message: '请输入字段名', trigger: 'blur' }],
      },
      secondaryAddFieldDialogVisible: false,
      secondaryNewFieldForm: { name: '' },
      
      // 关系选择相关
      selectedOrganizations: [],
      selectedPersons: [],
      selectedSecondaryPersons: [],
      currentSecondaryIndex: -1, // -1表示主组织，>=0表示次要目标人索引
      
      // 出生日期选择器配置
      birthDatePickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now(); // 禁用未来日期
        }
      },

      // 区号选择
      areaCodes: [
        { label: '+86 (中国)', value: '+86' },
        { label: '+1 (美国/加拿大)', value: '+1' },
        { label: '+44 (英国)', value: '+44' },
        { label: '+81 (日本)', value: '+81' },
        { label: '+852 (香港)', value: '+852' },
        { label: '+853 (澳门)', value: '+853' },
        { label: '+886 (台湾)', value: '+886' },
      ],
    };
  },
  created() {
    console.log("this.info", this.info);
    
    if (this.info && this.info._source && this.info._source.params) {
      // 主组织基础信息合并
      this.basicForm = this.info._source.params.basic;
      this.basicForm.relation = [];

      // 处理社交媒体账号
      this.basicForm.emails = this.info._source.params.emails || [];
      this.basicForm.phoneNumbers = this.info._source.params.phoneNumbers || [];
      this.basicForm.telegramIds = this.info._source.params.telegramIds || [];
      this.basicForm.twitterIds = this.info._source.params.twitterIds || [];
      this.basicForm.facebookIds = this.info._source.params.facebookIds || [];
      this.basicForm.linkedInIds = this.info._source.params.linkedInIds || [];

      // 处理自定义字段
      this.basicForm.customFields = this.info._source.params.customFields || {};

      // 处理次要目标人数据
      if (this.info._source.params.secondaryPersons) {
        this.secondaryPersons = this.info._source.params.secondaryPersons;
        this.secondaryPersons.forEach((person) => {
          // 确保数据结构完整
          if (!person.emails) this.$set(person, 'emails', []);
          if (!person.phoneNumbers) this.$set(person, 'phoneNumbers', []);
          if (!person.telegramIds) this.$set(person, 'telegramIds', []);
          if (!person.twitterIds) this.$set(person, 'twitterIds', []);
          if (!person.facebookIds) this.$set(person, 'facebookIds', []);
          if (!person.linkedInIds) this.$set(person, 'linkedInIds', []);
          if (!person.customFields) this.$set(person, 'customFields', {});
          if (!person.relation) this.$set(person, 'relation', []);
          if (!person.type) this.$set(person, 'type', 'secondary_key_person');
          if (!person.relationDesc) this.$set(person, 'relationDesc', '');
          if (!person.sex) this.$set(person, 'sex', '男');
        });
        this.initSecondaryNewInputs();
      }

      // 处理关系
      if (this.personRelationSearchList.length > 0) {
        this.personRelationSearchList.forEach(item => {
          this.basicForm.relation.push({
            intellValue: ['目标人'],
            personData: item,
            personRelation: "",
          });
        });
      }

      if (this.organizationRelationSearchList.length > 0) {
        this.organizationRelationSearchList.forEach(item => {
          this.basicForm.relation.push({
            intellValue: ['目标组织'],
            organizationData: item,
            organizationRelation: "",
          });
        });
      }
      console.log("basic:",this.basicForm);
      
    }
  },
  computed: {
    ...mapState({
      origanList: (state) => state.intellManageTree.origanList,
      personRelationSearchList: (state) => state.organizationTopologyMap.personRelationSearchList,
      organizationRelationSearchList: (state) => state.organizationTopologyMap.organizationRelationSearchList,
    }),
  },
  methods: {
    // --- 通用方法 ---
    reduceNumber() {
      let soleValue = Math.round(new Date().getTime() / 1000).toString();
      let random = ["a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n"];
      for (let i = 0; i < 6; i++) {
        let index = Math.floor(Math.random() * 13);
        soleValue += random[index];
      }
      return soleValue;
    },

    handleClose() {
      this.$confirm('您有未保存的更改，确定要关闭吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.closeDialog();
      }).catch(() => {});
    },

    closeDialog() {
      this.data.flag = false;
      this.basicForm = {
        avatar: "", name: "", remark: "", createTime: "", belong: "", desi: "",
        relation: [], emails: [], phoneNumbers: [], telegramIds: [], twitterIds: [], facebookIds: [], linkedInIds: [], customFields: {},
      };
      this.secondaryPersons = [];
      this.secondaryNewInputs = [];
      this.newEmail = ''; this.newPhoneAreaCode = '+86'; this.newPhoneNumber = '';
      this.newTelegramId = ''; this.newTwitterId = ''; this.newFacebookId = ''; this.newLinkedInId = '';
      this.activeName = "info";
      this.selectedOrganizations = [];
      this.selectedPersons = [];
      this.personFlag = false;
    },

    handleClickTabs(tab) {
      this.personFlag = false; // 切换tab时总是关闭关系面板
      if (tab.name === "info") {
        this.currentSecondaryIndex = -1;
      } else if (tab.name.startsWith('secondary-')) {
        this.currentSecondaryIndex = parseInt(tab.name.split('-')[1]);
      }
    },

    handleTabRemove(targetName) {
      const index = parseInt(targetName.split('-')[1]);
      this.removeSecondaryPerson(index);
    },
    
    // --- 头像上传 ---
    handleAvatarSuccess(res, file) {
      if (this.data.title == "添加目标组织") {
        this.basicForm.avatar = URL.createObjectURL(file.name);
      } else {
        this.info._source.params.basic.avatar = URL.createObjectURL(file.name);
      }
    },

    handleSecondaryAvatarSuccess(res, file, index) {
      this.secondaryPersons[index].avatar = URL.createObjectURL(file.raw);
    },

    beforeAvatarUpload(file) {
      const isJPGorPNG = file.type === "image/jpeg" || file.type === "image/png";
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isJPGorPNG) {
        this.$message.error("头像图片只能是 JPG/PNG 格式!");
        return false;
      }
      if (!isLt5M) {
        this.$message.error("头像图片大小不能超过 5MB!");
        return false;
      }

      // 上传文件
      this.uploadFile(file);
      return false;
    },

    uploadFile(file) {
      let formData = new FormData();
      formData.append("put_small_file", file);
      formData.append('file_type', 'icon');
      this.$axios.post('/filesystem/api/rest/v2/node-0/small_file/put_sha512_file', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }).then((response) => {
        console.log("response", response);
        if (response.status == 200) {
          this.handleAvatarSuccess(response.data, file);
        }
      }).catch((error) => {
        console.error("上传失败", error);
      });
    },

    // --- 关系管理 ---
    openRelationPanel(secondaryIndex) {
      this.currentSecondaryIndex = secondaryIndex !== undefined ? secondaryIndex : -1;
      this.prepareSelectedRelations(secondaryIndex);
      this.personFlag = true;
    },
    
    prepareSelectedRelations(secondaryIndex) {
      let relations = [];
      if (secondaryIndex !== undefined && this.secondaryPersons[secondaryIndex]) {
        relations = this.secondaryPersons[secondaryIndex].relation || [];
      } else {
        relations = this.basicForm.relation || [];
      }
      
      this.selectedOrganizations = relations
        .filter(r => r.intellValue && r.intellValue[0] === this.origanList[0].label && r.organizationData)
        .map(r => r.organizationData);
        
      this.selectedPersons = relations
        .filter(r => r.intellValue && r.intellValue[0] === this.origanList[1].label && r.personData)
        .map(r => r.personData);
        
      this.selectedSecondaryPersons = relations
        .filter(r => r.intellValue && r.intellValue[0] === this.origanList[2].label && r.personData)
        .map(r => r.personData);
    },

    handleSelectOrganizations(organizations) {
      let currentRelations;
      if (this.currentSecondaryIndex >= 0) {
        currentRelations = this.secondaryPersons[this.currentSecondaryIndex].relation;
      } else {
        currentRelations = this.basicForm.relation;
      }
      for (let i = currentRelations.length - 1; i >= 0; i--) {
        if (currentRelations[i].intellValue && currentRelations[i].intellValue[0] === this.origanList[0].label) {
          currentRelations.splice(i, 1);
        }
      }
      organizations.forEach(org => {
        currentRelations.push({
          intellValue: [this.origanList[0].label, org._source.params.basic.name],
          organizationData: org,
          organizationRelation: "",
        });
      });
    },

    handleSelectPersons(persons) {
      let currentRelations;
       if (this.currentSecondaryIndex >= 0) {
        currentRelations = this.secondaryPersons[this.currentSecondaryIndex].relation;
      } else {
        currentRelations = this.basicForm.relation;
      }
      for (let i = currentRelations.length - 1; i >= 0; i--) {
        if (currentRelations[i].intellValue && currentRelations[i].intellValue[0] === this.origanList[1].label) {
          currentRelations.splice(i, 1);
        }
      }
      persons.forEach(person => {
        currentRelations.push({
          intellValue: [this.origanList[1].label, person._source.params.basic.name],
          personData: person,
          personRelation: "",
        });
      });
    },
    
    handleSelectSecondaryPersons(persons) {
      let currentRelations;
      if (this.currentSecondaryIndex >= 0) {
        currentRelations = this.secondaryPersons[this.currentSecondaryIndex].relation;
      } else {
        currentRelations = this.basicForm.relation;
      }
      for (let i = currentRelations.length - 1; i >= 0; i--) {
        if (currentRelations[i].intellValue && currentRelations[i].intellValue[0] === this.origanList[2].label) {
          currentRelations.splice(i, 1);
        }
      }
      persons.forEach(person => {
        currentRelations.push({
          intellValue: [this.origanList[2].label, person._source.params.basic.name],
          personData: person,
          personRelation: "",
        });
      });
    },
    
    getRelationName(item) {
      if (item.intellValue && item.intellValue[0] === this.origanList[0].label && item.organizationData) {
        return item.organizationData?._source?.params?.basic?.name || item.organizationData?.basic?.name || '未知组织';
      } else if (item.intellValue && item.intellValue[0] === this.origanList[1].label && item.personData) {
        return item.personData?._source?.params?.basic?.name || item.personData?.basic?.name || '未知人员';
      } else if (item.intellValue && item.intellValue[0] === this.origanList[2].label && item.personData) {
        return item.personData?._source?.params?.basic?.name || item.personData?.basic?.name || '未知次要目标人';
      }
      return '未选择';
    },

    removeRelation(item) {
      this.$confirm('确定要删除该关系吗?', '提示', {
        confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
      }).then(() => {
        const index = this.basicForm.relation.indexOf(item);
        if (index > -1) this.basicForm.relation.splice(index, 1);
      }).catch(() => {});
    },

    removeSecondaryRelation(personIndex, item) {
      this.$confirm('确定要删除该关系吗?', '提示', {
        confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
      }).then(() => {
        const relations = this.secondaryPersons[personIndex].relation;
        const index = relations.indexOf(item);
        if (index > -1) relations.splice(index, 1);
      }).catch(() => {});
    },

    // --- 社交账号管理 ---
    validateEmail(email) {
      if (email && email.trim()) {
        const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
        return emailRegex.test(email);
      }
      return false;
    },

    validatePhoneNumber(phone) {
      const reg = /^\+?[0-9\s\-()]{7,15}$/;
      return reg.test(phone);
    },

    addSocialAccount(type) {
      const actionMap = {
        'emails': { value: this.newEmail, key: 'newEmail', validate: this.validateEmail },
        'phoneNumbers': { value: `${this.newPhoneAreaCode}-${this.newPhoneNumber}`, key: 'newPhoneNumber', areaCodeKey: 'newPhoneAreaCode' },
        'telegramIds': { value: this.newTelegramId, key: 'newTelegramId' },
        'twitterIds': { value: this.newTwitterId, key: 'newTwitterId' },
        'facebookIds': { value: this.newFacebookId, key: 'newFacebookId' },
        'linkedInIds': { value: this.newLinkedInId, key: 'newLinkedInId' },
      };
      const action = actionMap[type];
      const valueToAdd = (type === 'phoneNumbers') ? (this.newPhoneNumber.trim() ? action.value : '') : action.value.trim();

      if (valueToAdd) {
        if (action.validate && !action.validate(valueToAdd)) {
          this.$message.error(type === 'emails' ? '邮箱格式不正确' : '手机号格式不正确');
          return;
        }
        if (!this.basicForm[type].includes(valueToAdd)) {
          this.basicForm[type].push(valueToAdd);
          this[action.key] = '';
          if (action.areaCodeKey) this[action.areaCodeKey] = '+86';
        } else {
          this.$message.warning('该内容已存在');
        }
      }
    },

    removeSocialAccount(type, index) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
      }).then(() => {
        this.basicForm[type].splice(index, 1);
      }).catch(() => {});
    },

    addSecondarySocialAccount(personIndex, type) {
      const person = this.secondaryPersons[personIndex];
      const inputs = this.secondaryNewInputs[personIndex];
      const actionMap = {
        'emails': { value: inputs.email, key: 'email', validate: this.validateEmail },
        'phoneNumbers': { value: `${inputs.phoneAreaCode}-${inputs.phoneNumber}`, key: 'phoneNumber', areaCodeKey: 'phoneAreaCode' },
        'telegramIds': { value: inputs.telegramId, key: 'telegramId' },
        'twitterIds': { value: inputs.twitterId, key: 'twitterId' },
        'facebookIds': { value: inputs.facebookId, key: 'facebookId' },
        'linkedInIds': { value: inputs.linkedInId, key: 'linkedInId' },
      };
      const action = actionMap[type];
      const valueToAdd = (type === 'phoneNumbers') ? (inputs.phoneNumber.trim() ? action.value : '') : (action.value ? action.value.trim() : '');

      if (valueToAdd) {
        if (action.validate && !action.validate(valueToAdd)) {
          this.$message.error('格式不正确，请检查输入');
          return;
        }
        if (!person[type].includes(valueToAdd)) {
          person[type].push(valueToAdd);
          inputs[action.key] = '';
          if (action.areaCodeKey) inputs[action.areaCodeKey] = '+86';
        } else {
          this.$message.warning('该内容已存在');
        }
      }
    },

    removeSecondarySocialAccount(personIndex, type, index) {
      this.secondaryPersons[personIndex][type].splice(index, 1);
    },

    // --- 其他信息（自定义字段）管理 ---
    showAddFieldDialog() {
      this.newFieldForm.name = '';
      this.addFieldDialogVisible = true;
      this.$nextTick(() => this.$refs.newFieldForm.clearValidate());
    },

    addCustomField() {
      this.$refs.newFieldForm.validate(valid => {
        if (valid) {
          const fieldName = this.newFieldForm.name;
          if (!this.basicForm.customFields.hasOwnProperty(fieldName)) {
            this.$set(this.basicForm.customFields, fieldName, '');
            this.addFieldDialogVisible = false;
          } else {
            this.$message.warning('该字段名已存在');
          }
        }
      });
    },
    
    removeCustomField(fieldName) {
      this.$delete(this.basicForm.customFields, fieldName);
    },

    showSecondaryAddFieldDialog(index) {
      this.currentSecondaryIndex = index; // 记录当前操作的次要目标人索引
      this.secondaryNewFieldForm.name = '';
      this.secondaryAddFieldDialogVisible = true;
      this.$nextTick(() => this.$refs.secondaryNewFieldForm.clearValidate());
    },

    addSecondaryCustomField() {
      this.$refs.secondaryNewFieldForm.validate(valid => {
        if (valid && this.currentSecondaryIndex >= 0) {
          const fieldName = this.secondaryNewFieldForm.name;
          const customFields = this.secondaryPersons[this.currentSecondaryIndex].customFields;
          if (!customFields.hasOwnProperty(fieldName)) {
            this.$set(customFields, fieldName, '');
            this.secondaryAddFieldDialogVisible = false;
          } else {
            this.$message.warning('该字段名已存在');
          }
        }
      });
    },

    removeSecondaryCustomField(personIndex, fieldName) {
      this.$delete(this.secondaryPersons[personIndex].customFields, fieldName);
    },

    // --- 次要目标人管理 ---
    showSecondaryPersonDialog() {
      this.secondaryPersonForm.name = '';
      this.secondaryPersonDialogVisible = true;
      this.$nextTick(() => this.$refs.secondaryPersonForm.clearValidate());
    },

    addSecondaryPersonFn() {
      this.$refs.secondaryPersonForm.validate(valid => {
        if (valid) {
          const newSecondaryPerson = {
            name: this.secondaryPersonForm.name,
            avatar: "", remark: "", dateBirth: "", age: "", sex: "男", identity: "", email: "",
            relation: [], emails: [], phoneNumbers: [], telegramIds: [], twitterIds: [], facebookIds: [], linkedInIds: [], customFields: {},
            type: 'secondary_key_person', // 次要目标人类型
            relationDesc: '', // 与主组织的关系描述
            // 唯一ID
            id: this.reduceNumber(),
          };
          this.secondaryPersons.push(newSecondaryPerson);
          this.initSecondaryNewInputs();
          this.secondaryPersonDialogVisible = false;
        }
      });
    },

    removeSecondaryPerson(index) {
        this.$confirm('确定要删除此次要目标人吗?', '提示', {
            confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
        }).then(() => {
            const deletedName = `secondary-${index}`;
            this.secondaryPersons.splice(index, 1);
            this.secondaryNewInputs.splice(index, 1);
            // 如果删除的是当前激活的tab，则切换回主信息tab
            if (this.activeName === deletedName) {
                this.activeName = 'info';
            }
        }).catch(() => {});
    },
    
    initSecondaryNewInputs() {
        this.secondaryNewInputs = this.secondaryPersons.map(() => ({
            email: '', phoneAreaCode: '+86', phoneNumber: '', telegramId: '', twitterId: '', facebookId: '', linkedInId: '',
        }));
    },

    // 计算年龄的通用方法
    calculateAgeFromDate(dateString) {
      if (!dateString) return null;
      const birthDate = new Date(dateString);
      const currentDate = new Date();
      if (birthDate > currentDate) {
        this.$message.warning("出生日期不能选择未来时间");
        return null;
      }
      let age = currentDate.getFullYear() - birthDate.getFullYear();
      const monthDiff = currentDate.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && currentDate.getDate() < birthDate.getDate())) {
        age--;
      }
      return age;
    },

    // 计算次要目标人年龄
    calculateSecondaryAge(personIndex, dateString) {
      this.secondaryPersons[personIndex].age = this.calculateAgeFromDate(dateString);
    },

    // --- 表单提交与验证 ---
    submitForm() {
      this.checkAllTempInputValues();
      const promises = [this.$refs.basicForm.validate()];
      let relationMap = [];
      this.secondaryPersons.forEach((_, index) => {
        const formRefArray = this.$refs[`secondaryForm-${index}`];
        if (Array.isArray(formRefArray)) {
          if (formRefArray[0] && typeof formRefArray[0].validate === 'function') {
            promises.push(formRefArray[0].validate());
          }
        } else if (formRefArray && typeof formRefArray.validate === 'function') {
          promises.push(formRefArray.validate());
        }
      });

      Promise.all(promises).then(() => {
        if (this.secondaryPersons.length > 0) {
          // 为每个次要目标人生成唯一ID
          this.secondaryPersons.forEach(person => {
            if (!person.id) {
              person.id = this.reduceNumber();
            }
            this.$store.commit("organization/addSecondaryPerson", person);
            relationMap.push({
              id: person.id,
              relationDesc: person.relationDesc
            })
          });
        }
        this.basicForm['id'] = this.reduceNumber();
        window.main.$store.commit("organization/addOrgani", this.basicForm);
        if (relationMap.length > 0) {
          relationMap.forEach(item => {
            this.$store.commit("organization/addForwardRelation", {id: this.basicForm['id'], table: 'key_organization', targetID: item.id, relation: 'key_person', desc: item.relationDesc});
          });
        }
        this.closeDialog();
      }).catch(error => {
        console.error("表单验证失败", error);
        this.$message.error("表单验证失败，请检查输入");
      });
    },

    editForm() {
      this.checkAllTempInputValues();
      const promises = [this.$refs.basicForm.validate()];
      this.secondaryPersons.forEach((_, index) => {
        const formRefArray = this.$refs[`secondaryForm-${index}`];
        if (formRefArray && formRefArray.length > 0) {
            promises.push(formRefArray[0].validate());
        }
      });
      
      Promise.all(promises).then(() => {
        this.info._source.params.basic = this.basicForm;
        
        // 为每个次要目标人生成唯一ID（如果还没有ID）
        this.secondaryPersons.forEach(person => {
          if (!person.id) {
            person.id = this.reduceNumber();
          }
        });
        
        this.info._source.params.secondaryPersons = this.secondaryPersons;
        console.log("保存组织数据", this.info);
        // 替换为您的Vuex action
        window.main.$store.commit("organization/editOrganization", this.info);
        this.closeDialog();
      }).catch(error => {
        console.error("表单验证失败", error);
        this.$message.error("表单验证失败，请检查输入");
      });
    },
    
    checkAllTempInputValues() {
        if (this.newEmail.trim()) this.addSocialAccount('emails');
        if (this.newPhoneNumber.trim()) this.addSocialAccount('phoneNumbers');
        if (this.newTelegramId.trim()) this.addSocialAccount('telegramIds');
        if (this.newTwitterId.trim()) this.addSocialAccount('twitterIds');
        if (this.newFacebookId.trim()) this.addSocialAccount('facebookIds');
        if (this.newLinkedInId.trim()) this.addSocialAccount('linkedInIds');

        this.secondaryNewInputs.forEach((inputs, index) => {
            if (inputs.email.trim()) this.addSecondarySocialAccount(index, 'emails');
            if (inputs.phoneNumber.trim()) this.addSecondarySocialAccount(index, 'phoneNumbers');
            if (inputs.telegramId.trim()) this.addSecondarySocialAccount(index, 'telegramIds');
            if (inputs.twitterId.trim()) this.addSecondarySocialAccount(index, 'twitterIds');
            if (inputs.facebookId.trim()) this.addSecondarySocialAccount(index, 'facebookIds');
            if (inputs.linkedInId.trim()) this.addSecondarySocialAccount(index, 'linkedInIds');
        });
    }
  },
};
</script>

<style lang="scss" scoped>
/* 样式与页面A基本一致，此处为精简版，可直接复用页面A的样式 */
.OrigDialog {
  height: 100%;
  width: 100%;
}
.container-dialog {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  height: 65vh; /* 设定一个最大高度 */
  width: 100%;
}
.left {
  flex: 1;
  padding: 10px;
  overflow-y: auto;
  transition: width 0.3s ease;
}
.left-with-panel {
  width: calc(100% - 300px); /* 示例宽度 */
}
.right {
  width: 300px;
  padding: 10px;
  border-left: 1px solid #EBEEF5;
}
.personForm {
  width: 100%;
  max-width: 600px;
}
.secondary-person-btn-container {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 15px;
  padding: 0 10px;
}
.social-input-container {
  display: flex;
  align-items: center;
  width: 100%;
}
.social-input {
  flex: 1;
  margin-right: 10px;
}
.social-input-area-code {
  width: 140px;
  margin-right: 10px;
}
.social-input-phone-number {
  flex: 1;
  margin-right: 10px;
}
.social-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 5px;
  width: 100%;
}
.relation-box-container {
  width: 100%;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-top: 5px;
}
.relation-item {
  margin-bottom: 10px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 10px;
  background: #fff;
}
.relation-content {
  display: flex;
  align-items: center;
}
.relation-avatar {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
}
.relation-avatar-img {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
}
.relation-input {
  display: flex;
  align-items: center;
  flex: 1;
  margin: 0 10px;
}
.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 80px;
    height: 80px;
    line-height: 80px;
    text-align: center;
}
.avatar {
    width: 80px;
    height: 80px;
    display: block;
}
</style>